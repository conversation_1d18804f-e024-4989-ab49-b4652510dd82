@component('vendor.mail.html.message')

<!-- TODO: center align -->
@if(isset($header))
<span class="custom-user-markdown-header">
@endif
{{ Illuminate\Mail\Markdown::parse($header ?? '') }}
@if(isset($header))
</span>
@endif

@component('vendor.mail.html.subcopy')
{!! $content !!}
@endcomponent

@component('vendor.mail.html.footer')
{!! $footer ?? '' !!}
@endcomponent

@endcomponent

<style>
    .custom-user-markdown-header * {
        text-align: center;
    }
</style>
