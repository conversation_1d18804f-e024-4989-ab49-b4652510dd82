<script setup>
import {computed} from "vue";

const props = defineProps({
    darkMode: false,
    completed: Number,
    total: Number,
})

const progress = computed(() => {
    return props.completed / props.total * 100
})

const progressColor = computed(() => {
    if((props.completed / props.total * 100) <= 50) {
        return '#d25565'
    }
    else if ((props.completed / props.total * 100) > 50 && (props.completed / props.total * 100) < 90) {
        return '#efba58'
    }
    else {
        return '#6EE7B7'
    }
})

const themeClasses = computed(() => {
    if(props.darkMode === true) {
        return 'bg-dark-module border-dark-border text-slate-50'
    }
    else {
        return 'bg-light-module border-light-border text-slate-900'
    }
})
</script>

<template>
    <div class="w-full h-1 border-t relative" :class="themeClasses">
        <div class="absolute h-full transform transition-all duration-300 rounded-b-lg" :style="{ width: progress + '%', backgroundColor: progressColor }"></div>
    </div>
</template>

<style scoped>

</style>
