<template>
    <simple-card :dark-mode="darkMode">
        <div class="grid grid-cols-2 gap-4 p-4">
            <div class="flex flex-col gap-2">
                <labeled-value label="Email Template">
                    <email-template-dropdown
                        :dark-mode="darkMode"
                        :filters="{'email-template-type': [5]}"
                        v-model="modelValue.email_template_id"
                    />
                </labeled-value>
            </div>
            <div class="flex flex-col gap-2 relative">
                <labeled-value label="Start At">
                    <Datepicker
                        :dark-mode="darkMode"
                        v-model="modelValue.sent_at"
                        :min-date="new Date()"
                        :dark="darkMode"
                        teleport="body"
                    />
                </labeled-value>
            </div>
            <div class="flex flex-col gap-2 relative">
                <labeled-value>
                    <template #label>
                        <div class="flex gap-2 items-center font-semibold">
                            <div>Send Emails Between</div>
                            <simple-icon
                                :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                                :color="simpleIcon.colors.BLUE"
                                tooltip="Email will be sent at random point within the given range"
                            />
                        </div>
                    </template>
                    <div class="flex justify-between gap-2">
                        <Datepicker
                            :dark-mode="darkMode"
                            v-model="modelValue.send_time.start"
                            :dark="darkMode"
                            teleport="body"
                            time-picker
                            :clearable="false"
                            :max-time="modelValue.send_time.end"
                        />
                        <Datepicker
                            :dark-mode="darkMode"
                            v-model="modelValue.send_time.end"
                            :dark="darkMode"
                            teleport="body"
                            time-picker
                            :clearable="false"
                            :min-time="modelValue.send_time.start"
                        />
                    </div>
                </labeled-value>
            </div>
            <from-email-address :dark-mode="darkMode" v-model="modelValue" />
        </div>
    </simple-card>
</template>
<script>
import SimpleCard from "./SimpleCard.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Datepicker from "@vuepic/vue-datepicker";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import EmailTemplateDropdown from "./EmailTemplateDropdown.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import FromEmailAddress from "./FromEmailAddress.vue";
const simpleIcon = useSimpleIcon();
export default {
    name: "DripEmailConfiguration",
    components: {
        FromEmailAddress,
        SimpleIcon,
        EmailTemplateDropdown,
        LabeledValue,
        Datepicker,
        LoadingSpinner,
        SimpleCard
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            simpleIcon,
        }
    }
}
</script>
