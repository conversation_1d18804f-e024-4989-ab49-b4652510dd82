import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'marketing-campaigns', 1);
    }

    listMarketingCampaigns(params = {}) {
        return this.axios().get('/', { params })
    }

    getMarketingDomains(params) {
        return this.axios().get('/domains', {params: params})
    }

    syncMarketingDomains() {
        return this.axios().post('/domains/sync');
    }

    getMarketingCampaignOptions(params) {
        return this.axios().get('/options', {params: params})
    }

    getMarketingCampaignCallbackTypes() {
        return this.axios().get('/callback-types')
    }

    getMarketingCampaignTypes(params) {
        return this.axios().get('/types', {params: params})
    }

    getMarketingCampaignShortcodes() {
        return this.axios().get('/shortcodes')
    }

    updateMarketingCampaignTargets(params) {
        return this.axios().patch('/', params)
    }

    createMarketingCampaign(params) {
        return this.axios().post('/', params)
    }

    listMarketingCampaignConsumers(params = {}) {
        return this.axios().get('/consumers/', { params})
    }

    listMarketingCampaignLogs(params = {}) {
        return this.axios().get('/logs', {params})
    }

    getMarketingLog(marketingLogId) {
        return this.axios().get(`/logs/${marketingLogId}`)
    }

    getMarketingCampaign(marketingCampaignId) {
        return this.axios().get(`/${marketingCampaignId}`)
    }

    getMarketingCampaignConsumer(marketingCampaignConsumerId) {
        return this.axios().get(`/consumers/${marketingCampaignConsumerId}`);
    }

    updateMarketingCampaign(marketingCampaignId, marketingCampaign) {
        return this.axios().patch(`/${marketingCampaignId}`, {campaign: marketingCampaign})
    }

    updateCampaignMetrics(campaignId) {
        return this.axios().post(`${campaignId}/update-metrics`)
    }

    toggleCampaign(campaignId) {
        return this.axios().post(`${campaignId}/toggle`)
    }

    sendTestEmail(
        emailTemplateId, fromEmail, fromName, toEmail,
    ) {
        return this.axios().post(`send-test-email`, {
            emailTemplateId,
            fromEmail,
            fromName,
            toEmail
        })
    }

    sendTestSMS(
        toPhone, message,
    ) {
        return this.axios().post(`send-test-sms`, {
            to_phone: toPhone,
            message
        })

    }

    deleteMarketingCampaign(campaignId) {
        return this.axios().delete(`${campaignId}`)
    }

    filterOptions() {
        return this.axios().get(`filter-options`);
    }

    getEstimate(campaign) {
        return this.axios().get(`estimate`, {params: campaign})
    }

}
