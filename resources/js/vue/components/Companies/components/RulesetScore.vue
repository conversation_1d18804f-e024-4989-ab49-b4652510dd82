<template>
    <div class="relative flex items-center cursor-default">
        <div @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave" class="flex flex-col items-center justify-center">
            <div class="p-1 flex flex-col items-center justify-center text-center text-white rounded-lg drop-shadow-lg" :style="{backgroundColor: color}">
            <span class="w-[61px] font-extrabold text-sm">
              {{ formattedScore }}
            </span>
            </div>
        </div>
        <div v-if="showPopover"
             class="absolute left-[80px] flex gap-2 flex-col items-center justify-center z-100 p-4 rounded-lg drop-shadow-lg whitespace-nowrap text-sm"
             :class="[darkMode ? 'bg-dark-module border border-dark-border' : 'bg-light-module border-light-border']">
            <p class="font-bold">{{ score.ruleset.name }}</p>
            <ul v-if="formattedScore">
                <li>Score in percentage: <strong>{{ formattedScore }}</strong></li>
                <li>Score in points: <strong>{{ this.score.score_data.total_score_in_points }}</strong></li>
                <li>Total points available: <strong>{{ this.score.score_data.total_points_available }}</strong></li>
                <li>Calculated at: <strong>{{ $filters.dateFromTimestamp(this.score.calculated_at, 'us') }}</strong></li>
            </ul>
            <p v-else class="font-semibold">Not calculated</p>
        </div>
    </div>
</template>
<script>
export default {
    name: "RulesetScore",
    props: {
        score: {
            type: Object,
            required: true
        },

        darkMode: {
            type: Boolean,
            required: true
        }
    },

    data(){
        return {
            showPopover: false,
        }
    },

    computed: {
        formattedScore(){
            return this.score && +this.score.score >= 0 ? this.score.score + '%' : 'N/A';
        },
        color(){
            const redColor = '#ff0000'
            const greenColor = '#16a34a'

            return this.formattedScore ? this.generateGradient(this.score.score, redColor, greenColor) : 'red';
        },
    },

    methods: {
        generateGradient(value, colorFrom, colorTo) {
            const rgbFrom = this.hexToRgb(colorFrom);
            const rgbTo = this.hexToRgb(colorTo);

            const red = Math.round(this.lerp(rgbFrom.r, rgbTo.r, value / 100));
            const green = Math.round(this.lerp(rgbFrom.g, rgbTo.g, value / 100));
            const blue = Math.round(this.lerp(rgbFrom.b, rgbTo.b, value / 100));

            const hex = this.rgbToHex(red, green, blue);

             return `#${hex}`;
        },

        hexToRgb(hex) {
            const normalizedHex = hex.replace('#', '');
            const bigint = parseInt(normalizedHex, 16);
            const r = (bigint >> 16) & 255;
            const g = (bigint >> 8) & 255;
            const b = bigint & 255;
            return { r, g, b };
        },

        rgbToHex(r, g, b) {
            const componentToHex = (c) => {
                const hex = c.toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            };
            return componentToHex(r) + componentToHex(g) + componentToHex(b);
        },

        // Linear interpolation
        lerp(start, end, t) {
            return start * (1 - t) + end * t;
        },

        handleMouseLeave(){
            this.showPopover = false
        },

        handleMouseEnter(){
            this.showPopover = true
        },
    }
}
</script>
