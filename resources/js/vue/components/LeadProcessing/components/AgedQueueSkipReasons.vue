<template>
    <div id="comments" class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}" v-if="reasons.length">
        <div class="pb-6">
            <div class="pt-5 px-5 pb-4 flex justify-between">
                <h5 class=" text-primary-500 text-sm uppercase font-semibold leading-tight">Skip Reasons</h5>
                <slot name="header-actions"></slot>
            </div>
            <div v-for="reason in reasons" class="grid grid-cols-2 gap-2 items-center px-5 py-2 border-b"
                 :class="{ 'text-grey-800 hover:bg-light-module border-light-border': !darkMode, 'text-grey-200 hover:bg-dark-module border-dark-border': darkMode }">
                <div class="text-xs">
                    <p>{{ reason.processor_name }}</p>
                    <p> {{ getFormattedDate(reason.timestamp * 1000) }}</p>
                </div>
                <div class="text-xs " >
                    {{ reason.reason }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ConsumerApiService from "../../Shared/services/consumer_api.js";

export default {
    name: "AgedQueueSkipReasons",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        consumerProductId: {
            type: Number,
            required: true
        }
    },
    data () {
        return {
            api: ConsumerApiService.make(),
            reasons: []
        }
    },
    created() {
        this.api.getAgedQueueSkipReasons(this.consumerProductId).then(resp => this.reasons = resp.data.data.reasons);
    },
    methods: {
        getFormattedDate(timestamp) {
            return new Date(timestamp).toLocaleString()
        }
    }
}
</script>
