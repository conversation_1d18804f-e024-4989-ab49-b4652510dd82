<template>
    <loading-spinner v-if="pendingApprovalsStore.loading"/>
    <div v-else-if="!pendingApprovalsStore.loading && pendingApprovalsStore.pendingApprovalCount > 0">
        <div class="flex items-center">
            <p class="font-semibold flex-1">Pending approvals ({{ pendingApprovalsStore.pendingApprovalCount }})</p>
            <simple-icon
                v-if="!pendingApprovalsStore.loading"
                @click="toggleShowPendingApprovals"
                :icon="showPendingApprovals ? simpleIcon.icons.CHEVRON_UP : simpleIcon.icons.CHEVRON_DOWN"
                clickable
            />
        </div>
        <div v-if="showPendingApprovals" class="flex flex-col gap-2 pr-4 max-h-48 overflow-auto">
            <div v-for="approval in pendingApprovalsStore.pendingApprovals.data"
                 class="p-4 rounded-lg w-full flex shadow border gap-6 items-center"
                 :class="[getCustomColorColor(approval)]"
            >
                <div class="grid grid-cols-8 flex-1 items-center gap-3">
                    <labeled-value
                        label="Action"
                        :class="[
                                approval.reviewed_at ? 'col-span-3' : 'col-span-5'
                            ]"
                    >
                        <component
                            v-if="getApprovalSummaryComponent(approval)"
                            :is="getApprovalSummaryComponent(approval)"
                            :approval="approval"
                        />
                        <div v-else>{{ approval.requested_action }}</div>
                    </labeled-value>
                    <labeled-value label="Request" class="col-span-2">
                        <div class="flex flex-col gap-1">
                            <div class="flex gap-1 items-center">
                                <simple-icon :icon="simpleIcon.icons.REQUESTER" tooltip="Requester"/>
                                <p class="font-semibold truncate">{{ approval.requested_by?.name }}</p>
                            </div>
                            <div class="flex gap-1 items-center">
                                <simple-icon :icon="simpleIcon.icons.CALENDAR" tooltip="Requested at"/>
                                <p>{{ approval.requested_at }}</p>
                            </div>
                        </div>
                    </labeled-value>
                    <labeled-value v-if="approval.reviewed_at" label="Review" class="col-span-2">
                        <div class="flex flex-col gap-1">
                            <div class="flex gap-1 items-center">
                                <simple-icon :icon="simpleIcon.icons.REVIEWER" tooltip="Reviewer"/>
                                <p class="font-semibold truncate">{{ approval.reviewed_by?.name }}</p>
                            </div>
                            <div class="flex gap-1 items-center">
                                <simple-icon :icon="simpleIcon.icons.CALENDAR" tooltip="Reviewed at"/>
                                <p>{{ approval.reviewed_at }}</p>
                            </div>
                        </div>
                    </labeled-value>
                    <invoice-action-request-status-badge class="h-fit w-fit" :status="approval.status"/>
                </div>
                <simple-icon
                    :icon="simpleIcon.icons.ARROW_UP_RIGHT_FROM_SQUARE"
                    tooltip="Open approval"
                    @click="openApproval(approval)"
                    :clickable="hasFinanceRole"
                    :class="!hasFinanceRole ? 'cursor-not-allowed' : ''"
                ></simple-icon>
            </div>
        </div>
    </div>
</template>
<script>
import LabeledValue from "../LabeledValue.vue";
import LoadingSpinner from "../LoadingSpinner.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import EntityHyperlink from "../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../Badge.vue";
import InvoiceActionRequestStatusBadge
    from "../../../BillingManagement/Tabs/InvoceActionRequest/InvoiceActionRequestStatusBadge.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import AllocateCreditToCompanySummary from "./ActionSummary/AllocateCreditToCompanySummary.vue";
import {markRaw} from "vue";
import ExpireCompanyCreditSummary from "./ActionSummary/ExpireCompanyCreditSummary.vue";
import ExtendCompanyCreditSummary from "./ActionSummary/ExtendCompanyCreditSummary.vue";
import useQueryParams from "../../../../../composables/useQueryParams.js";
import {usePendingApprovals} from "../../../../../stores/pending-approvals.store.js";
import {useRolesPermissions, ROLES} from "../../../../../stores/roles-permissions.store.js";

const queryParamsHelper = useQueryParams()
export default {
    name: "DisplayPendingActions",
    components: {
        AllocateCreditToCompanySummary,
        InvoiceActionRequestStatusBadge,
        Badge,
        EntityHyperlink,
        SimpleIcon,
        LoadingSpinner,
        LabeledValue,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        relatedType: {
            type: String,
            required: true
        },
        relatedId: {
            type: [String, Number],
            required: true
        },
    },
    data() {
        return {
            showPendingApprovals: true,
            pendingApprovalsStore: usePendingApprovals(),
            rolesPermissions: useRolesPermissions()
        }
    },
    mounted() {
        this.pendingApprovalsStore.listPendingApprovals({relatedId: this.relatedId, relatedType: this.relatedType})
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        hasFinanceRole(){
            return this.rolesPermissions.hasAnyRole([
                ROLES.FINANCE_CONTROLLER,
                ROLES.FINANCE_OWNER,
            ])
        }
    },
    methods: {
        getCustomColorColor(approval) {
            return {
                [this.pendingApprovalsStore.statuses.approved]: 'border-green-500',
                [this.pendingApprovalsStore.statuses.rejected]: 'border-red-500',
                [this.pendingApprovalsStore.statuses.pending]: 'border-yellow-500'
            }[approval.status] ?? ''
        },
        toggleShowPendingApprovals() {
            this.showPendingApprovals = !this.showPendingApprovals
        },
        getApprovalSummaryComponent(approval) {
            return {
                apply_credit_to_company: markRaw(AllocateCreditToCompanySummary),
                update_invoice_status: '', // TODO - Create summary components for each action
                issue_invoice_to_collections: '',
                write_off_invoice: '',
                issue_invoice_refund: '',
                expire_company_credit: markRaw(ExpireCompanyCreditSummary),
                extend_company_credit: markRaw(ExtendCompanyCreditSummary),
            }[approval.requested_action_slug]
        },
        openApproval(approval) {
            const url = queryParamsHelper.mountUrlWithSearchParams({
                tab: 'Action Requests',
                id: approval.id
            }, '/billing-management')

            window.open(url, '_blank')
        },
    }
}
</script>
