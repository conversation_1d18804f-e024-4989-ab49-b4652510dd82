<template>
    <div>
        <div class="border rounded-lg py-5"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
        >
            <div class="px-5 pb-5 flex items-center justify-between">
                <h5 class="text-blue-550 text-sm uppercase font-semibold leading-tight">Recyclable Leads Search</h5>
                <div class="flex items-center gap-x-2"
                     v-if="selectedGrouping !== RecyclableConsumerGrouping.NONE"
                >
                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']"
                       class="whitespace-nowrap"
                    >
                        Estimated Conversion %:
                    </p>
                    <div class="max-w-24">
                        <CustomInput
                            :dark-mode="darkMode"
                            v-model="estimatedConversionRate"
                            type="number"
                        />
                    </div>
                </div>
            </div>
            <div class="flex items-center flex-wrap gap-3 px-5 pb-5">
                <CustomButton type="submit" :dark-mode="darkMode"
                              @click="submitSearch"
                >
                    Search
                </CustomButton>
                <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset"
                              @click="resetFilters"
                >
                    Reset
                </CustomButton>
                <Filterable
                    :dark-mode="darkMode"
                    :filters="filters"
                    v-model="filterInputs"
                    @update:defaults="updateFilterDefaults"
                    @update:filterOptions="getFilterOptionUpdates"
                />
                <div class="flex items-center gap-x-3 min-w-[16rem] ml-2">
                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']"
                       class="block mb-1 whitespace-nowrap"
                    >
                        Group by:
                    </p>
                    <Dropdown
                        :dark-mode="darkMode"
                        :options="groupingOptions"
                        v-model="selectedGrouping"
                        :selected="selectedGrouping"
                        type="number"
                    />
                </div>
            </div>
            <FilterableActivePills
                class="px-8 mb-6"
                v-if="filters.length"
                :filters="filters"
                :active-filters="filterInputs"
                :dark-mode="darkMode"
                @reset-filter="clearFilter"
            />
            <recyclable-lead-search-table-header
                :dark-mode="darkMode"
                :leads="leads"
                @select-all="selectAllLeads"
                @contact-leads="contactLeads"
                :processing="processing"
                :grouping="returnedGrouping"
            />
            <loading-spinner v-if="loading" />
            <recyclable-lead-search-table-body
                v-else-if="searched"
                :dark-mode="darkMode"
                :leads="leads"
                :processing="processing"
                :grouping="returnedGrouping"
                :conversion-rate="estimatedConversionRate"
            />
            <div class="p-3 flex items-center justify-end gap-2">
                <div>
                    <span class="text-sm text-slate-500">Results Per Page</span>
                </div>
                <div>
                    <Dropdown
                        :dark-mode="darkMode"
                        placement="top"
                        :options="perPageOptions"
                        v-model="perPageSelection"
                        :selected="25"
                        @update:modelValue="() => handlePaginationEvent({newPage: 1})"
                    />
                </div>
                <Pagination
                    :dark-mode="darkMode"
                    :pagination-data="paginationData"
                    :show-pagination="true"
                    @change-page="handlePaginationEvent"
                />
            </div>
        </div>
        <alerts-container :dark-mode="darkMode" :alert-type="alertType" :text="alertText" v-if="alertActive"></alerts-container>
    </div>
</template>

<script>
import ApiService from "./services/api.js";
import Dropdown from "../Shared/components/Dropdown.vue";
import Pagination from "../Shared/components/Pagination.vue";
import RecyclableLeadSearchTableHeader from "./components/RecyclableLeadSearchTableHeader.vue";
import RecyclableLeadSearchTableBody from "./components/RecyclableLeadSearchTableBody.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import Autocomplete from "../Shared/components/Autocomplete.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import Filterable from "../Shared/components/Filterables/Filterable.vue";
import FilterableActivePills from "../Shared/components/Filterables/FilterableActivePills.vue";
import { nextTick } from "vue";
import CustomInput from "../Shared/components/CustomInput.vue";

export const RecyclableConsumerGrouping = {
    NONE: 0,
    STATE: 1,
    COUNTY: 2,
    ZIP_CODE: 3,
}

export default {
    name: "RecycledLeadSearch",
    components: {
        CustomInput,
        FilterableActivePills,
        Filterable,
        CustomButton,
        Autocomplete,
        AlertsContainer, LoadingSpinner, RecyclableLeadSearchTableBody, RecyclableLeadSearchTableHeader, Pagination, Dropdown},
    mixins:[AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            api: ApiService.make(),
            leads: [],
            paginationData: {},
            perPageOptions: [ { id: 10, name: "10" }, { id: 25, name: "25" }, { id: 50, name: "50" }, { id: 100, name: "100" } ],
            perPageSelection: 10,
            page: 1,
            loading: false,
            processing: false,

            filters: [],
            filterInputs: {},
            filterDefaults: {},

            selectedGrouping: 0,
            returnedGrouping: 0,
            groupingOptions: [],

            estimatedConversionRate: 10,
            RecyclableConsumerGrouping,
            searched: false,
            maximumBatch: 500
        }
    },
    created() {
        this.initialise();
    },
    computed: {
        params: function () {
            return {
                page: this.page,
                per_page: this.perPageSelection,
                grouping: this.selectedGrouping,
                filters: this.filterInputs,
            }
        },
        selectedLeads: function () {
            if (this.returnedGrouping > 0) {
                return this.leads.filter(lead => lead.selected).reduce((allLeads, lead) => [...allLeads, ...lead.consumer_product_ids ?? []], []);
            }

            return this.leads.filter(lead => lead.selected).map(lead => lead.id);
        }
    },
    methods: {
        initialise() {
            this.loading = true;
            this.getSearchOptions();
        },
        submitSearch() {
            this.searched = true;
            this.getLeads();
        },
        getLeads() {
            this.loading = true;
            this.leads = [];
            this.api.searchLeads(this.params)
                .then(resp => {
                    if (resp.data.data.status) {
                        this.leads = resp.data.data.consumers.data;
                        this.paginationData = resp.data.data.consumers;
                        this.returnedGrouping = resp.data.data.grouping;

                        this.leads.forEach(lead => lead.selected = false);
                    }
                })
                .catch(e => this.showAlert('error',  e.response.data.message))
                .finally(() => this.loading = false);
        },
        handlePaginationEvent(newPageEvent) {
            this.page = newPageEvent.newPage ?? 1
            this.getLeads();
        },
        selectAllLeads(selectAll) {
            if (selectAll) {
                this.leads.forEach(lead => lead.selected = true);
            } else {
                this.leads.forEach(lead => lead.selected = false);
            }
        },
        contactLeads() {
            if (this.selectedLeads.length > this.maximumBatch) {
                this.showAlert('error', `Batch limit: Maximum of ${this.maximumBatch} leads`);
                return;
            }

            this.processing = true;
            this.api.contactLeads({
                leads: this.selectedLeads
            }).then(resp => {
                this.leads.forEach(lead => lead.selected = false);
                this.showAlert('success', 'The selected leads will be added to the Aged queue shortly')
            }).catch(e => this.showAlert('error', e.response.data.message))
                .finally(() => this.processing = false);
        },
        async resetFilters() {
            await nextTick();
            this.filters.forEach(filter => {
                this.filterInputs[filter.id] = this.filterDefaults?.[filter.id] ?? null;
            });
        },
        selectFilter(filter) {
            this.selectedFilter = filter;
            this.selectedOption = null;
        },
        selectOption(option) {
            this.selectedOption = option;
        },
        clearFilter(filterId) {
            delete this.filterInputs[filterId];
        },
        updateFilterDefaults(filterChange) {
            Object.assign(this.filterDefaults, { ...filterChange });
        },
        getSearchOptions() {
            this.loading = true;

            this.api.getFilterOptions().then(resp => {
                if (resp.data?.data?.status) {
                    this.filters = resp.data.data.filter_options ?? [];
                    this.groupingOptions = Object.entries(resp.data.data.grouping_options ?? {}).map(([key, value]) => ({id: parseInt(key), name: value}));
                    this.selectedGrouping = this.groupingOptions.find(option => option.id === RecyclableConsumerGrouping.COUNTY)?.id ?? "0";
                }
            }).catch(err => {
                this.showAlert('error', err.message);
            }).finally(() => {
                this.loading = false;
            });
        },
        getFilterOptionUpdates() {
            this.api.getFilterOptionUpdates({
                filters: this.filterInputs,
            }).then(resp => {
                if (resp.data?.data?.status) {
                    this.updateFilterOptions(resp.data.data.filter_updates ?? null);
                }
            }).catch(err => {
                this.showAlert('error', err.message);
            });
        },
        // This method handles Filterables with child Filterables which need updating when query is submitted
        updateFilterOptions(updatedFilterOptions) {
            if (updatedFilterOptions) {
                for (const filterKey in updatedFilterOptions) {
                    const targetIndex = this.filters.findIndex(filter => {
                        return filter.id === filterKey;
                    });
                    if (targetIndex >= 0) this.filters[targetIndex] = updatedFilterOptions[filterKey];
                    this.validateInputsOnUpdatedFilter(updatedFilterOptions[filterKey]);
                }
            }
        },
        validateInputsOnUpdatedFilter(updatedFilter) {
            if (!this.filterInputs) return;

            const filterValues = (filterComponent, parentId) => {
                const id = filterComponent.id;
                if (id in this.filterInputs[parentId]) {
                    const validValues = Object.values(filterComponent.options);
                    this.filterInputs[parentId][id] = this.filterInputs[parentId][id].filter(input => validValues.includes(input));
                }
            }
            const parentId = updatedFilter.id;

            if (!parentId in this.filterInputs || !this.filterInputs[parentId]) return;

            filterValues(updatedFilter, parentId);
            for (const child in (updatedFilter.children ?? {})) {
                filterValues(updatedFilter.children[child], parentId);
            }
        }
    }
}
</script>
