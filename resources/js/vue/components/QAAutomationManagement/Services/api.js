class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    static make() {
        return new ApiService('internal-api', 'qa-automation', 1);
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getIndustryServiceConfigurations() {
        return this.axios().get('/industry-service/')
    }

    createIndustryServiceConfigurations(payload) {
        return this.axios().post('/industry-service/', payload)
    }

    updateIndustryServiceConfigurations(payload) {
        return this.axios().patch(`/industry-service/${payload.id}`, payload)
    }

    deleteIndustryServiceConfigurations(id) {
        return this.axios().delete(`/industry-service/${id}`)
    }

    getRules() {
        return this.axios().get('/rule/')
    }

    createRule(payload) {
        return this.axios().post('/rule/', payload)
    }

    updateRule(id, payload) {
        return this.axios().patch(`/rule/${id}`, payload)
    }

    deleteRule(id) {
        return this.axios().delete(`/rule/${id}`)
    }

}

const qaAutomationApiService = ApiService.make();
export default qaAutomationApiService;
