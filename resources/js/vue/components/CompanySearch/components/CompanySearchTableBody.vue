<template>
    <tr v-if="loading" class="pb-5 mb-7 text-center"
        :class="{
        'px-5 min-h-[140px] py-5 gap-5 text-sm font-semibold items-center border-b': true,
        'text-slate-100 bg-dark-background': darkMode,
        'text-slate-500 bg-light-background': !darkMode,
        }">
        <td class="mb-3" :colspan="totalColumnsEnabled" >
            <loading-spinner :dark-mode="darkMode" ></loading-spinner>
            Loading Companies
        </td>
    </tr>
    <tr v-else-if="!loading && data.length === 0" class="pb-5 mb-4 text-center"
        :class="{
        'px-5 min-h-[140px] py-5 gap-5 text-sm items-center border-b': true,
        'text-slate-100 bg-dark-background hover:bg-dark-module': darkMode,
        'text-slate-900 bg-light-background hover:bg-light-module': !darkMode,
        }">
        <td :colspan="totalColumnsEnabled" class="pb-5 mb-4"
            :class="{
                'px-5 min-h-[140px] py-5 gap-5 text-sm font-semibold items-center': true,
                'text-slate-100 bg-dark-background': darkMode,
                'text-slate-500 bg-light-background': !darkMode,
             }">
            No Companies Found.
        </td>
    </tr>
    <tbody v-else>
        <tr v-for="(item, key) in data" :key="item?.id"
            class="pb-3 mb-4"
             :class="{
                'px-5 min-h-[140px] py-5 gap-5 text-sm items-center': true,
                'text-slate-100 bg-dark-background hover:bg-dark-module': darkMode,
                'text-slate-900 bg-light-background hover:bg-light-module': !darkMode,
             }">
            <TableCell>
                <input type="checkbox" class="form-checkbox h-4 w-4 text-primary-500 rounded"
                       :id="`checkbox-${key}`"
                       :value="item"
                       v-model="useSelectionStore().selection"
                >
            </TableCell>
            <TableCell v-show="fields.id.shown" :id="`id-${key}`">
                <a target="_blank" :href="`/companies/${item.id}`"
                   class="text-primary-500 font-bold flex items-center">
                    {{ item?.id ?? 'N/A' }}
                </a>
            </TableCell>
            <TableCell v-show="fields.companyName.shown" :id="`name-${key}`">
                <a target="_blank" :href="`/companies/${item.id}`"
                   class="text-primary-500 font-bold flex items-center">
                    {{ item?.name ?? item?.entity_name ?? 'N/A' }}
                </a>
            </TableCell>
            <TableCell v-show="fields.status.shown" :id="`status-${key}`">
                <company-status-badge :dark-mode="darkMode" :company="item" />
            </TableCell>
            <TableCell v-show="fields.salesStatus.shown" :id="`sales-status-${key}`">
                {{ item?.sales_status ?? 'N/A' }}
            </TableCell>
            <TableCell v-show="fields.industries.shown" :id="`industries-${key}`">
                <template v-if="item?.industries?.length > 0">
                    <div class="flex items-center">
                        <p
                            class="peer text-primary-500 cursor-default text-sm inline-flex items-center font-semibold text-center hover:cursor-pointer">
                            {{ item?.industries?.length }} {{item?.services?.length  > 1 ? "Industries" : "Industry"}}
                        </p>
                        <div
                            class="hidden peer-hover:grid z-50 rounded-md shadow-module p-2 gap-2 border absolute left-[40%]"
                            :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                        >
                            <p class="text-sm whitespace-nowrap font-medium"
                               v-for="industry in item?.industries ?? []">
                                {{ industry }}
                            </p>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <p class="text-sm text-slate-500 text-center">N/A</p>
                </template>
            </TableCell>
            <TableCell v-show="fields.services.shown" :id="`services-${key}`">
                <template v-if="item?.services?.length > 0">
                    <div class="flex items-center">
                        <p
                            class="peer text-primary-500 cursor-default text-sm inline-flex items-center font-semibold text-center hover:cursor-pointer">
                            {{ item?.services?.length }} {{item?.services?.length  > 1 ? "Services" : "Service"}}
                        </p>
                        <div
                            class="hidden peer-hover:grid z-50 rounded-md shadow-module p-2 gap-2 border absolute left-[40%]"
                            :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                        >
                            <p class="text-sm whitespace-nowrap font-medium"
                               v-for="service in item?.services ?? []">
                                {{ service }}
                            </p>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <p class="text-sm text-slate-500 text-center">N/A</p>
                </template>
            </TableCell>
            <TableCell v-show="fields.addresses.shown" :id="`addresses-${key}`">
                <template v-if="item?.addresses?.length > 0">
                    <p class="text-sm">
                        <span v-if="streetOne(item)">{{ streetOne(item) }}</span><br>
                        <span v-if="streetOne(item) && streetTwo(item)">{{ streetTwo(item) }}</span><br
                        v-if="streetTwo(item)">
                        <span v-if="city(item) && zipCode(item)">{{
                                city(item) + ', ' + zipCode(item)
                            }}</span>
                    </p>
                </template>
                <template v-else>
                    <p class="text-sm text-slate-500 text-center">N/A</p>
                </template>
            </TableCell>
            <TableCell v-show="fields.state.shown" :id="`state-${key}`">
                <template v-if="item?.addresses?.length > 0">
                    <p class="text-sm flex flex-col items-center">
                        <span v-if="state(item)">{{ state(item) }}</span>
                        <span v-if="item?.addresses?.length > 1 && allStates(item).length > 0" class="block cursor-pointer italic" :title="allStates(item).join(', ')">
                            (+{{ allStates(item).length }})
                        </span>
                    </p>
                </template>
                <template v-else>
                    <p class="text-sm text-slate-500 text-center">N/A</p>
                </template>
            </TableCell>
            <TableCell v-show="fields.campaigns.shown" :id="`campaigns-${key}`">
                <p><b class="mb-2">Active:</b>&nbsp;{{ item?.campaigns_active_count || 'N/A' }}</p>
                <p><b>Total:</b>&nbsp;{{ item?.campaigns_total || 'N/A' }}</p>
            </TableCell>
            <TableCell v-show="fields.leadsPurchased.shown" :id="`lead-cost-one-${key}`">
                {{ item?.lead_cost_one_formatted ?? item?.total_cost_spent ?? 'N/A' }}
            </TableCell>
            <TableCell v-show="fields.leadsPurchased.shown && leadsPurchasedColumnTwoIsEnabled"
                       :id="`lead-cost-two-${key}`">
                {{ item?.lead_cost_two_formatted ?? 'N/A' }}

            </TableCell>
            <TableCell v-show="fields.lastTimeLeadsPurchased.shown"
                       :id="`last-time-lead-purchased-${key}`">
                {{ item?.last_time_lead_received || 'N/A' }}
            </TableCell>
            <TableCell v-show="fields.leadRejection.shown" :id="`manual-lead-rejection-percentage-${key}`">
                {{
                    item?.lead?.rejection_percentage?.manual != null ? item?.lead?.rejection_percentage?.manual + '%' : 'N/A'
                }}
            </TableCell>
            <TableCell v-show="fields.leadRejection.shown" :id="`crm-lead-rejection-percentage-${key}`">
                {{
                    item?.lead?.rejection_percentage?.crm != null ? item?.lead?.rejection_percentage?.crm + '%' : 'N/A'
                }}
            </TableCell>
            <TableCell v-show="fields.leadRejection.shown" :id="`overall-lead-rejection-percentage-${key}`">
                {{
                    item?.lead?.rejection_percentage?.overall != null ? item?.lead?.rejection_percentage?.overall + '%' : 'N/A'
                }}
            </TableCell>
            <TableCell v-show="fields.lastTimeContacted.shown"
                       :id="`last-time-contacted-${key}`">
                <div v-if="item?.last_contacted_at_type && item?.last_contacted_at_date">
                    <p>{{item?.last_contacted_at_direction}} {{ item?.last_contacted_at_type }}</p>
                    <p>{{ item?.last_contacted_at_date }}</p>
                </div>
                <div v-else>
                    N/A
                </div>
            </TableCell>
            <TableCell v-show="fields.lastTimeCalled.shown"
                       :id="`last-time-called-${key}`">
                {{ item?.last_time_called || 'N/A' }}
            </TableCell>
            <TableCell v-show="fields.googleRating.shown"
                       :id="`google-rating-${key}`">
                <div class="flex flex-row items-center gap-x-1">
                    <div> {{ item?.google_rating ?? '-' }}</div>
                </div>
            </TableCell>
            <TableCell v-show="fields.googleReviewCount.shown"
                       :id="`google-review-count-${key}`">
                <div class="flex flex-row items-center gap-x-1">
                    <div> {{ item?.google_review_count ?? '-' }}</div>
                </div>
            </TableCell>
            <TableCell v-show="fields.estimatedRevenue.shown"
                :id="`estimated-revenue-${key}`">
                <div class="flex items-center">
                    <div class="peer" :class="{
                        'text-primary-500 cursor-default text-sm inline-flex items-center font-semibold text-center hover:cursor-pointer': isEstimatedRevenueAtLeastOneThousand(item),
                    }">
                        {{
                            determineEstimatedRevenue(item)
                        }}
                    </div>
                    <div
                        v-if="isEstimatedRevenueAtLeastOneThousand(item)"
                        class="hidden peer-hover:grid rounded-md p-2 gap-2 border shadow-module transform"
                        :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                    >
                        {{ determineFullEstimatedRevenue(item) }}
                    </div>
                </div>
            </TableCell>
            <TableCell v-show="fields.estimatedMonthlyAdSpend.shown"
                       :id="`estimated-monthly-add-spend-${key}`"
                       >
                <div class="flex flex-row items-center gap-x-1">
                    <div v-if="item?.estimated_monthly_ad_spend">{{item?.estimated_monthly_ad_spend}}</div>
                    <div v-else>-</div>
                </div>
            </TableCell>
            <TableCell v-show="fields.cadenceName.shown" :id="`cadence-name-${key}`">
                <p>{{ item.cadence_name ?? 'N/A' }}</p>
            </TableCell>
            <TableCell v-show="fields.lifetimeRevenue.shown" :id="`lifetime-revenue-${key}`">
                <p>{{ item.lifetime_revenue ?? 'N/A' }}</p>
            </TableCell>
            <TableCell v-show="fields.other.shown" :id="`other-${key}`">
                <p><b class="my-2">Reviews</b>:&nbsp;{{ item?.review_count || '0' }}</p>
                <p><b class="my-2">Last Login</b>:&nbsp;{{ item?.last_login || 'N/A' }}</p>
                <p><b class="my-2">Revised</b>:&nbsp;{{ item?.last_revised || 'N/A' }}</p>
                <p><b class="my-2">Pre-screened</b>:&nbsp;{{ item?.prescreened ? 'yes' : 'no'}}</p>
            </TableCell>
            <TableCell v-show="fields.actions.shown" :id="`actions-${key}`">
                <delete-company-button
                    :dark-mode="darkMode"
                    :company-id="item?.id"
                    :deletable="item?.deletable"
                    :queued="item?.queued"
                    @finished="$emit('search')"
                />
            </TableCell>
            <TableCell v-show="fields.bdm.shown" :id="`bdm-${key}`">
                <p>{{ item?.bdm || 'N/A' }}</p>
            </TableCell>
            <TableCell v-show="fields.am.shown" :id="`am-${key}`">
                <p>{{ item?.am || 'N/A' }}</p>
            </TableCell>
            <TableCell v-show="fields.om.shown" :id="`om-${key}`">
                <p>{{ item?.om || 'N/A' }}</p>
            </TableCell>
        </tr>
    </tbody>
</template>

<script>
import TableCell from '../shared/TableCell.vue'
import {mapState} from 'pinia'
import {useFieldsToggleStore, useSelectionStore} from '../services/service'
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Badge from "../../Shared/components/Badge.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import DeleteCompanyModal from "../../Companies/components/Delete/DeleteCompanyModal.vue";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import CompanyStatusBadge from "./CompanyStatusBadge.vue";
import DeleteCompanyButton from "../../Companies/components/Delete/DeleteCompanyButton.vue";

/**
 * @typedef {Object} company
 * @property {number} id
 * @property {string} name
 * @property {string} entity_name
 * @property {string} consolidated_status
 * @property {string} consolidated_status_object
 * @property {string} types
 * @property {address[]} addresses
 * @property {number} campaigns_active_count
 * @property {number} campaigns_total
 * @property {number} lead_rejection_percentage
 * @property {number} appointment_rejection_percentage
 * @property {number} total_cost_spent
 * @property {number} review_count
 * @property {string} last_login
 * @property {string} last_revised
 * @property {number} lead_cost
 * @property {number} lead_cost_one
 * @property {number} lead_cost_two
 * @property {string} lead_cost_one_formatted
 * @property {string} lead_cost_two_formatted
 * @property {number} last_contacted_at
 * @property {string} last_contacted_at_type
 * @property {string} last_contacted_at_date
 * @property {string} last_contacted_at_direction
 * @property {string} last_time_called
 * @property {string} last_time_lead_received
 * @property {string|number} google_rating
 * @property {string|number} google_review_count
 * @property {number} estimated_revenue
 * @property {number} estimated_monthly_ad_spend
 */

export default {
    name: 'CompanySearchTableBody',
    components: {
        DeleteCompanyButton,
        CompanyStatusBadge,
        LabeledValue,
        Tooltip,
        AlertsContainer, DeleteCompanyModal, CustomButton, LoadingSpinner, TableCell, Badge },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        data: {
            type: Array,
            default: []
        },
        filterInputs: {
            type: Object,
            default: {}
        },
        currentPage: {
            type: Number,
            default: 1
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    emits: ['search'],
    data () {
    },
    methods: {
        /**
         * @typedef {Object} item
         * @property {number} estimated_revenue
         * @param item
         */
        determineFullEstimatedRevenue (item) {
            const amount = item?.estimated_revenue

            return !!amount
                ? this.$filters.currency(amount, { precision: 0 })
                : (amount !== 0 ? '-' : this.$filters.currency(0, { precision: 0 }))
        },
        isEstimatedRevenueAtLeastOneThousand (item) {
            const amount = item?.estimated_revenue

            return amount >= 1000
        },
        /**
         * @typedef {Object} item
         * @property {number} estimated_revenue
         * @param item
         */
        determineEstimatedRevenue (item) {
            let result = 0
            let suffix = ''
            const precision = 0

            let amount = item?.estimated_revenue

            if (isNaN(amount)) amount = Number(amount)

            switch (true) {
                case amount >= 1000000000000:
                    result = amount / 1000000000000
                    suffix = 'T'
                    return this.$filters.currency(result, { precision }) + suffix
                case amount >= 1000000000:
                    result = amount / 1000000000
                    suffix = 'B'
                    return this.$filters.currency(result, { precision }) + suffix
                case amount >= 1000000:
                    result = amount / 1000000
                    suffix = 'M'
                    return this.$filters.currency(result, { precision }) + suffix
                case amount >= 1000:
                    result = amount / 1000
                    suffix = 'K'
                    return this.$filters.currency(result, { precision }) + suffix
                default:
                    return this.determineFullEstimatedRevenue(item)
            }
        },
        useSelectionStore,
        address (item) {
            return item?.addresses[0].address
        },
        streetOne (item) {
            const address = this.address(item)

            return address?.address_1 ?? null
        },
        streetTwo (item) {
            const address = this.address(item)

            return address?.address_2 ?? null
        },
        city (item) {
            const address = this.address(item)

            return address?.city ?? null
        },
        state (item) {
            const address = this.address(item)

            return address?.state ?? null
        },
        allStates(item) {
            const headquarterState = this.state(item);
            const uniqueStates = [...new Set(item.addresses?.map(address => address.state).filter(v => v) ?? [])];
            const filteredStates = uniqueStates.filter(state => state !== headquarterState);

            return filteredStates;
        },
        zipCode (item) {
            const address = this.address(item)

            return address?.zip_code ?? null
        },
    },
    computed: {
        ...mapState(useFieldsToggleStore, {
            fields: state => state.fieldsGetter,
        }),
        leadsPurchasedColumnTwoIsEnabled () {
            const leadsPurchasedObject = this.filterInputs.hasOwnProperty('company-leads-purchased') ? this.filterInputs['company-leads-purchased'] : null

            if (leadsPurchasedObject) {
                const logical = leadsPurchasedObject?.logical
                const secondInput = leadsPurchasedObject?.second_input
                const secondOperator = leadsPurchasedObject?.second_operator

                return ['and', 'or'].includes(logical) && !isNaN(secondInput) && ['equalTo', 'lessThan', 'greaterThan'].includes(secondOperator)
            }

            return false
        },
        totalColumnsEnabled() {
            return Object.values(this.fields).reduce((total, field) => field.shown ? total + 1 : total, 0) + 1;
        },
    }
}
</script>

<style scoped>

</style>
