import {markRaw} from "vue";
import MonthlyFrequency from "../vue/components/Shared/components/Simple/MonthlyFrequency.vue";
import WeeklyFrequency from "../vue/components/Shared/components/Simple/WeeklyFrequency.vue";
import DaysFrequency from "../vue/components/Shared/components/Simple/DaysFrequency.vue";
import useNumberHelper from "./useNumberHelper.js";

export function useSimpleFrequencyHelper() {
    const numberHelper = useNumberHelper();

    const frequencies = {
        MONTHLY: 'monthly',
        WEEKLY: 'weekly',
        DAILY: 'daily',
    }

    const frequencyOptions = [
        {
            name: 'Monthly',
            id: frequencies.MONTHLY,
            component: markRaw(MonthlyFrequency),
            defaults: {
                month_day: 3,
            },
            getTableText({month_day} = {}){
                const ordNumber = numberHelper.getOrdinalSuffix(month_day)
                return `On the ${ordNumber} day of every month`
            }
        },
        {
            name: 'Weekly',
            id: frequencies.WEEKLY,
            component: markRaw(WeeklyFrequency),
            defaults: {
                week_day: 'monday'
            },
            getTableText: ({week_day}) => `On every ${week_day}`
        },
        {
            name: 'Daily',
            id: frequencies.DAILY,
            component: markRaw(DaysFrequency),
            defaults: {
                day: 7,
            },
            getTableText: ({day} = {}) => `Every ${day} days from the last billed at`
        }
    ]

    const getFrequencyData = (selectedType) => {
        const found = frequencyOptions.find(f => f.id === selectedType)

        if (!found) {
            throw new Error('Frequency type not found')
        }

        return found
    }

    const getHumanReadableText = (type, data) => {
        const frequencyData = getFrequencyData(type)

        return frequencyData.getTableText(data)
    }

    return {
        frequencies,
        frequencyOptions,
        getFrequencyData,
        getHumanReadableText
    }
}
