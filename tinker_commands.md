# Tinker Commands for SendMonthlyCostSummaryEmailJob

## Quick Dispatch
```php
// Simple dispatch
dispatch(new App\Jobs\Reports\SendMonthlyCostSummaryEmailJob());

// Dispatch to specific queue
App\Jobs\Reports\SendMonthlyCostSummaryEmailJob::dispatch()->onQueue('email_notification');

// Dispatch with delay
App\Jobs\Reports\SendMonthlyCostSummaryEmailJob::dispatch()->delay(now()->addMinutes(5));
```

## Check Company Data
```php
// Check company exists
$company = App\Models\Odin\Company::find(App\Jobs\Reports\SendMonthlyCostSummaryEmailJob::COMPANY_ID);
echo "Company: " . $company->name;

// Check active contacts
$contacts = App\Models\Odin\CompanyUser::where('company_id', $company->id)
    ->where('status', App\Models\Odin\CompanyUser::STATUS_ACTIVE)
    ->where('is_contact', App\Models\Odin\CompanyUser::USER_IS_CONTACT)
    ->whereNotNull('email')
    ->where('email', '!=', '')
    ->get();
echo "Active contacts: " . $contacts->count();
```

## Check Product Assignments
```php
// Check current month assignments
$count = App\Models\Odin\ProductAssignment::where('company_id', App\Jobs\Reports\SendMonthlyCostSummaryEmailJob::COMPANY_ID)
    ->where('delivered', true)
    ->where('chargeable', true)
    ->whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()])
    ->count();
echo "Assignments this month: " . $count;
```

## Monitor Job
```php
// Check failed jobs
$failed = DB::table('failed_jobs')->where('payload', 'like', '%SendMonthlyCostSummaryEmailJob%')->get();
echo "Failed jobs: " . $failed->count();

// Check job status (if using database queue)
$jobs = DB::table('jobs')->where('payload', 'like', '%SendMonthlyCostSummaryEmailJob%')->get();
echo "Pending jobs: " . $jobs->count();
```
