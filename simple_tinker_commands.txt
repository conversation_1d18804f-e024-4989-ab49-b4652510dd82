# Simple Tinker Commands (copy and paste directly)

## Quick Dispatch (one-liner)
dispatch(new App\Jobs\Reports\SendMonthlyCostSummaryEmailJob());

## Check Company and Dispatch
$company = App\Models\Odin\Company::find(App\Jobs\Reports\SendMonthlyCostSummaryEmailJob::COMPANY_ID); echo "Company: " . ($company ? $company->name : 'NOT FOUND'); if($company) dispatch(new App\Jobs\Reports\SendMonthlyCostSummaryEmailJob());

## Check Everything Before Dispatch
$companyId = App\Jobs\Reports\SendMonthlyCostSummaryEmailJob::COMPANY_ID; $company = App\Models\Odin\Company::find($companyId); $contacts = $company ? App\Models\Odin\CompanyUser::where('company_id', $companyId)->where('status', 1)->where('is_contact', 1)->whereNotNull('email')->count() : 0; $assignments = $company ? App\Models\Odin\ProductAssignment::where('company_id', $companyId)->where('delivered', true)->where('chargeable', true)->whereBetween('created_at', [now()->startOfMonth(), now()])->count() : 0; echo "Company: " . ($company ? $company->name : 'NOT FOUND') . "\nContacts: $contacts\nAssignments (month to date): $assignments\nDate range: " . now()->startOfMonth()->format('M j') . " to " . now()->format('M j, Y') . "\n"; if($company && $contacts > 0) { dispatch(new App\Jobs\Reports\SendMonthlyCostSummaryEmailJob()); echo "✅ Job dispatched!"; } else { echo "❌ Cannot dispatch - missing company or contacts"; }
