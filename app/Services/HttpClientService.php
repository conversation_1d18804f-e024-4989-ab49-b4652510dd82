<?php

namespace App\Services;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Throwable;

class HttpClientService
{
    protected PendingRequest $client;

    public function __construct(
        ?string $baseUrl = null,
        array $headers = [],
        ?int $timeout = null
    )
    {
        $this->client = Http::withHeaders($headers);

        if ($baseUrl) {
            $this->client->baseUrl($baseUrl);
        }

        if ($timeout) {
            $this->client->timeout($timeout);
        }
    }

    /**
     * @param string $uri
     * @param array $query
     * @param array $headers
     * @return array
     * @throws RequestException|ConnectionException
     */
    public function get(string $uri, array $query = [], array $headers = []): array
    {
        return $this->request('get', $uri, [
            'query'   => $query,
            'headers' => $headers,
        ]);
    }

    /**
     * @param string $uri
     * @param array $data
     * @param array $headers
     * @return array
     * @throws RequestException|ConnectionException
     */
    public function post(string $uri, array $data = [], array $headers = []): array
    {
        return $this->request('post', $uri, [
            'json'    => $data,
            'headers' => $headers,
        ]);
    }

    /**
     * @param string $uri
     * @param array $data
     * @param array $headers
     * @return array
     * @throws RequestException|ConnectionException
     */
    public function put(string $uri, array $data = [], array $headers = []): array
    {
        return $this->request('put', $uri, [
            'json'    => $data,
            'headers' => $headers,
        ]);
    }

    /**
     * @param string $uri
     * @param array $data
     * @param array $headers
     * @return array
     * @throws RequestException|ConnectionException
     */
    public function delete(string $uri, array $data = [], array $headers = []): array
    {
        return $this->request('delete', $uri, [
            'json'    => $data,
            'headers' => $headers,
        ]);
    }

    /**
     * @param string $method
     * @param string $uri
     * @param array $options
     * @return array
     * @throws RequestException|ConnectionException
     */
    protected function request(string $method, string $uri, array $options): array
    {
        $client = $this->client->withHeaders(Arr::get($options, 'headers', []));

        try {
            $response = match (strtolower($method)) {
                'get'   => $client->get($uri, Arr::get($options, 'query', [])),
                default => $client->{$method}($uri, Arr::get($options, 'json', [])),
            };
        } catch (ConnectionException $exception) {
            throw new HttpResponseException(
                response()->json([
                    'message' => 'Service is unavailable.',
                    'error'   => $exception->getMessage(),
                ], 503)
            );
        }

        $body = $response->body();
        $isJson = str_starts_with($response->header('Content-Type'), 'application/json');

        if ($response->failed()) {
            $json = [];

            if ($isJson) {
                $decoded = json_decode($body, true);
                $json = is_array($decoded) && !empty($decoded)
                    ? $decoded
                    : ['message' => 'Empty or invalid JSON response', 'raw' => $body];
            } else {
                throw new HttpResponseException(
                    response($body, $response->status())
                );
            }

            throw new HttpResponseException(
                response()->json($json, $response->status())
            );
        }

        if ($isJson) {
            $decoded = json_decode($body, true);
            if (is_array($decoded)) {
                return $decoded;
            }
        }

        return [
            'message' => 'Expected JSON but received non-JSON response.',
            'content' => $body,
        ];
    }
}
