<?php

namespace App\Http\Requests\Affiliate;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ListAffiliatesRequest extends FormRequest
{
    const string REQUEST_NAME = 'name';
    const string REQUEST_FROM = 'from';
    const string REQUEST_TO = 'to';
    const string REQUEST_DATE_RANGE = 'date_range';
    const string REQUEST_PAGE = 'page';
    const string REQUEST_PER_PAGE = 'per_page';
    const string REQUEST_SORT_BY = 'sort_by';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::AFFILIATES->value);
    }

    public function validator($factory)
    {
        return $factory->make(
            $this->sanitize(), $this->container->call([$this, 'rules']), $this->messages()
        );
    }

    public function sanitize(): array
    {
        $this->merge([
            self::REQUEST_DATE_RANGE => json_decode($this->input(self::REQUEST_DATE_RANGE), true)
        ]);
        return $this->all();
    }

    public function rules(): array
    {
        $paginationRules = [
            self::REQUEST_PAGE => 'nullable|numeric|min:1',
            self::REQUEST_PER_PAGE => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,
            self::REQUEST_NAME => ['sometimes', 'string'],
            self::REQUEST_DATE_RANGE => ['required', 'array'],
            self::REQUEST_DATE_RANGE . '.' . self::REQUEST_FROM => ['sometimes', 'date'],
            self::REQUEST_DATE_RANGE . '.' . self::REQUEST_TO => ['sometimes', 'date'],
            self::REQUEST_SORT_BY => ['sometimes', 'array'],
        ];
    }
}
