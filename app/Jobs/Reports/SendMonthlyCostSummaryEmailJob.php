<?php

namespace App\Jobs\Reports;

use App\Mail\Reports\MonthlyCostSummaryEmail;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendMonthlyCostSummaryEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int COMPANY_ID = 1; // Replace with actual company ID

    public int $tries = 3;
    public int $maxExceptions = 3;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(config('queue.named_queues.email_notification'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $company = Company::find(self::COMPANY_ID);
        
        if (!$company) {
            return;
        }

        $costSummaryData = $this->getMonthlyCostSummaryData();
        
        if ($costSummaryData->isEmpty()) {
            return;
        }

        $activeContacts = $this->getActiveCompanyContacts($company);
        
        if ($activeContacts->isEmpty()) {
            return;
        }

        $this->sendEmailToContacts($activeContacts, $costSummaryData, $company);
    }

    /**
     * Get monthly cost summary data grouped by company campaign
     */
    private function getMonthlyCostSummaryData(): Collection
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();

        return ProductAssignment::query()
            ->select([
                'campaign_id',
                DB::raw('SUM(cost) as total_cost'),
                DB::raw('COUNT(*) as assignment_count')
            ])
            ->where(ProductAssignment::FIELD_COMPANY_ID, self::COMPANY_ID)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->whereBetween(ProductAssignment::FIELD_CREATED_AT, [$startOfMonth, $endOfMonth])
            ->groupBy('campaign_id')
            ->with(['campaign:id,name,reference'])
            ->get()
            ->map(function ($item) {
                return [
                    'campaign_name' => $item->campaign->name ?? 'Unknown Campaign',
                    'campaign_reference' => $item->campaign->reference ?? 'N/A',
                    'total_cost' => number_format($item->total_cost, 2),
                    'assignment_count' => $item->assignment_count,
                ];
            });
    }

    /**
     * Get active company contacts for the company
     */
    private function getActiveCompanyContacts(Company $company): Collection
    {
        return EloquentCompanyContact::query()
            ->where(EloquentCompanyContact::FIELD_COMPANY_ID, $company->legacy_id)
            ->where(EloquentCompanyContact::FIELD_STATUS, EloquentCompanyContact::STATUS_ACTIVE)
            ->whereNotNull(EloquentCompanyContact::FIELD_EMAIL)
            ->where(EloquentCompanyContact::FIELD_EMAIL, '!=', '')
            ->get();
    }

    /**
     * Send email to all active contacts
     */
    private function sendEmailToContacts(Collection $contacts, Collection $costSummaryData, Company $company): void
    {
        $monthYear = Carbon::now()->format('F Y');
        
        foreach ($contacts as $contact) {
            try {
                Mail::to($contact->email)->send(
                    new MonthlyCostSummaryEmail($costSummaryData, $company, $monthYear, $contact)
                );
            } catch (\Exception $e) {
                // Log the error but continue with other contacts
                \Log::error('Failed to send monthly cost summary email', [
                    'company_id' => self::COMPANY_ID,
                    'contact_email' => $contact->email,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
