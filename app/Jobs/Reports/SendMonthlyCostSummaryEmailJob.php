<?php

namespace App\Jobs\Reports;

use App\Mail\Reports\MonthlyCostSummaryEmail;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendMonthlyCostSummaryEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int COMPANY_ID = 15158; // Replace with actual company ID

    public int $tries = 3;
    public int $maxExceptions = 3;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(config('queue.named_queues.email_notification'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $company = Company::find(self::COMPANY_ID);
            logger()->info('Sending monthly cost summary email for company id ' . self::COMPANY_ID);
            if (!$company) {
                return;
            }
logger()->info('Getting cost summary data');
            $costSummaryData = $this->getMonthlyCostSummaryData();
logger()->info('Cost summary data: ' . $costSummaryData->count());
            if ($costSummaryData->isEmpty()) {
                return;
            }
            logger()->info('Cost summary data: ' . $costSummaryData->count());
            $activeContacts = $this->getActiveCompanyContacts($company);

            if ($activeContacts->isEmpty()) {
                return;
            }
            logger()->info('Active contacts: ' . $activeContacts->count());
            $this->sendEmailToContacts($activeContacts, $costSummaryData, $company);
        } catch (\Exception $e) {
            logger()->error('Failed to send monthly cost summary email: ' . $e->getMessage());
        }
    }
    /**
     * Get monthly cost summary data grouped by company campaign
     */
    private function getMonthlyCostSummaryData(): Collection
    {
        $startOfMonth = Carbon::now()->startOfYear();
        $endOfMonth = Carbon::now()->endOfMonth();

        return ProductAssignment::query()
            ->select([
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID . ' as campaign_id',
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_NAME . ' as campaign_name',
                DB::raw('SUM(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . ') as total_cost'),
                DB::raw('COUNT(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID . ') as lead_count')
            ])
            ->join(Budget::TABLE, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_BUDGET_ID, '=', Budget::TABLE . '.' . Budget::FIELD_ID)
            ->join(BudgetContainer::TABLE, Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE . '.' . BaseCompanyCampaignModule::FIELD_ID)
            ->join(CompanyCampaign::TABLE, BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, self::COMPANY_ID)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
            ->whereBetween(ProductAssignment::TABLE . '.' . ProductAssignment::CREATED_AT, [$startOfMonth, $endOfMonth])
            ->groupBy([
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_NAME
            ])
            ->get()
            ->map(function ($item) {
                return [
                    'campaign_name' => $item->campaign_name ?? 'Unknown Campaign',
                    'total_cost' => number_format($item->total_cost, 2),
                    'lead_count' => $item->lead_count,
                ];
            });
    }

    /**
     * Get active company users for the company
     */
    private function getActiveCompanyContacts(Company $company): Collection
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $company->id)
            ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
            ->where(CompanyUser::FIELD_IS_CONTACT, CompanyUser::USER_IS_CONTACT)
            ->whereNotNull(CompanyUser::FIELD_EMAIL)
            ->where(CompanyUser::FIELD_EMAIL, '!=', '')
            ->get();
    }

    /**
     * Send email to all active contacts
     */
    private function sendEmailToContacts(Collection $contacts, Collection $costSummaryData, Company $company): void
    {
logger()->info('Sending email to contacts');
        foreach ($contacts as $contact) {
            try {
                logger()->info('Sending email to contact ' . $contact->email);
                Mail::to($contact->email)->send(
                    new MonthlyCostSummaryEmail($costSummaryData, $company, $contact)
                );
            } catch (\Exception $e) {
                // Log the error but continue with other contacts
                \Log::error('Failed to send monthly cost summary email', [
                    'company_id' => self::COMPANY_ID,
                    'contact_email' => $contact->email,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
