<?php

namespace App\Builders\Affiliates;

use App\Models\Affiliates\Payout;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class ConsumerProductAffiliatesBuilder
{
    public function __construct(
        protected Builder $query
    )
    {
    }

    /**
     * @return self
     */
    public static function query(): self
    {
        $query = ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_AFFILIATE_PAYOUT)
            ->with([
                ConsumerProduct::RELATION_ADDRESS            => [
                    Address::RELATION_ZIPCODE
                ],
                ConsumerProduct::RELATION_CONSUMER,
                ConsumerProduct::RELATION_AFFILIATE_PAYOUT . '.'. Payout::RELATION_STRATEGY,
                ConsumerProduct::RELATION_SERVICE_PRODUCT    => [
                    ServiceProduct::RELATION_SERVICE => [
                        IndustryService::RELATION_INDUSTRY
                    ]
                ],
                ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT => function ($has) {
                    $has
                        ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                        ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true);
                }
            ]);

        return new self($query);
    }

    /**
     * @param int|null $affiliateId
     * @return ConsumerProductAffiliatesBuilder
     */
    public function forAffiliateId(?int $affiliateId = null): self
    {
        if (filled($affiliateId)) {
            $this->query->whereHas(ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD, function ($query) use ($affiliateId) {
                $query->where(ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID, '=', $affiliateId);
            });
        }

        return $this;
    }

    /**
     * @param int|null $campaignId
     * @return ConsumerProductAffiliatesBuilder
     */
    public function forCampaignId(?int $campaignId = null): self
    {
        if (filled($campaignId)) {
            $this->query->whereHas(ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD, function ($query) use ($campaignId) {
                $query->where(ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID, '=', $campaignId);
            });
        }

        return $this;
    }

    public function betweenTimestamps(
        ?int $timestampFrom = null,
        ?int $timestampTo = null,
    ): self
    {
        if (filled($timestampFrom) && filled($timestampTo)) {
            $this->query->whereBetween(ConsumerProduct::FIELD_CREATED_AT, [
                Carbon::createFromTimestamp($timestampFrom, 'UTC'),
                Carbon::createFromTimestamp($timestampTo, 'UTC'),
            ]);
        }

        return $this;
    }

    /**
     * @return Builder
     */
    function getQuery(): Builder
    {
        return $this->query;
    }
}
