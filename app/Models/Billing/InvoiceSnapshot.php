<?php

namespace App\Models\Billing;

use App\Enums\Billing\InvoiceStates;
use App\Helpers\CarbonHelper;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;


/**
 * @property int $id
 * @property string $uuid
 * @property int $invoice_id
 * @property int $company_id
 * @property int $account_manager_id
 * @property int $success_manager_id
 * @property string $status
 * @property string $relation_type
 * @property int $relation_id
 * @property float $total_value
 * @property float $total_outstanding
 * @property float $total_refunded
 * @property float $total_paid
 * @property float $total_collections
 * @property float $total_collections_recovered
 * @property float $total_collections_lost
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Company|null $company
 * @method static Builder mostRecentByInvoice(?array $filters = [])
 * @method static Builder hasRelevantOutstandingAmount()
 */
class InvoiceSnapshot extends Model
{
    use HasFactory;

    const string TABLE = 'invoice_snapshots';

    const string FIELD_ID                              = 'id';
    const string FIELD_UUID                            = 'uuid';
    const string FIELD_INVOICE_ID                      = 'invoice_id';
    const string FIELD_COMPANY_ID                      = 'company_id';
    const string FIELD_ACCOUNT_MANAGER_ID              = 'account_manager_id';
    const string FIELD_SUCCESS_MANAGER_ID              = 'success_manager_id';
    const string FIELD_BUSINESS_DEVELOPMENT_MANAGER_ID = 'business_development_manager_id';
    const string FIELD_STATUS                          = 'status';
    const string FIELD_RELATION_TYPE                   = 'relation_type';
    const string FIELD_RELATION_ID                     = 'relation_id';
    const string FIELD_TOTAL_VALUE                     = 'total_value';
    const string FIELD_TOTAL_OUTSTANDING               = 'total_outstanding';
    const string FIELD_TOTAL_REFUNDED                  = 'total_refunded';
    const string FIELD_TOTAL_PAID                      = 'total_paid';
    const string FIELD_TOTAL_COLLECTIONS               = 'total_collections';
    const string FIELD_TOTAL_COLLECTIONS_RECOVERED     = 'total_collections_recovered';
    const string FIELD_TOTAL_COLLECTIONS_LOST          = 'total_collections_lost';
    const string FIELD_TOTAL_CREDIT_APPLIED            = 'total_credit_applied';
    const string FIELD_TOTAL_CHARGEBACK                = 'total_chargeback';
    const string FIELD_TOTAL_CHARGEBACK_WON            = 'total_chargeback_won';
    const string FIELD_TOTAL_CHARGEBACK_LOST           = 'total_chargeback_lost';
    const string FIELD_TOTAL_WRITTEN_OFF               = 'total_written_off';
    const string FIELD_DATE                            = 'date';
    const string FIELD_CREATED_AT                      = 'created_at';
    const string FIELD_UPDATED_AT                      = 'updated_at';

    const string RELATION_ACCOUNT_MANAGER              = 'accountManager';
    const string RELATION_SUCCESS_MANAGER              = 'successManager';
    const string RELATION_RELATIONSHIP_MANAGER         = 'relationshipManager';
    const string RELATION_BUSINESS_DEVELOPMENT_MANAGER = 'businessDevelopmentManager';
    const string RELATION_COMPANY                      = 'company';
    const string RELATION_INVOICE                      = 'invoice';

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @param Builder $query
     * @param array $filters
     * @return void
     */
    public static function scopeMostRecentByInvoice(
        Builder $query,
        array $filters = []
    ): void
    {
        $dateFrom = Arr::get($filters, 'date_from');
        $dateTo = Arr::get($filters, 'date_to');
        $excludeCreditPaid = Arr::get($filters, 'exclude_credit_paid');

        $columnName = InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_ID;

        $subQuery = InvoiceSnapshot::query()
            ->from(DatabaseHelperService::database() . '.' . InvoiceSnapshot::TABLE)
            ->selectRaw("MAX($columnName) as last_snapshot_id")
            ->groupBy(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID)
            ->when($dateFrom, function ($query) use ($dateFrom) {
                $query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_DATE, '>=', CarbonHelper::parseWithTimezone($dateFrom)->endOfDayUTC());
            })
            ->when($dateTo, function ($query) use ($dateTo) {
                $query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_DATE, '<=', CarbonHelper::parseWithTimezone($dateTo)->endOfDayUTC());
            })
            ->when($excludeCreditPaid, function ($query) {
                $query->whereNot(function ($query) {
                    $query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_STATUS, InvoiceStates::ISSUED)
                        ->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING, 0);
                });
            });

        $query->from(DatabaseHelperService::database() . '.' . InvoiceSnapshot::TABLE)->joinSub($subQuery, 'sub', function ($join) {
            $join->on('sub.last_snapshot_id', '=', InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_ID);
        });
    }

    /**
     * @param Builder $query
     * @return void
     */
    public static function scopeHasRelevantOutstandingAmount(Builder $query): void
    {
        $query->where(function ($query) {
            $query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING, '>', 0)
                ->whereIn(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_STATUS, [
                    InvoiceStates::ISSUED->value,
                    InvoiceStates::FAILED->value,
                ])
                ->orWhere(function ($query) {
                    $query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK, '>', 0)
                        ->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_LOST, 0)
                        ->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_WON, 0);
                });
        });
    }
    /**
     * @return BelongsTo
     */
    public function accountManager(): BelongsTo
    {
        return $this->belongsTo(CompanyUserRelationship::class, self::FIELD_ACCOUNT_MANAGER_ID, CompanyUserRelationship::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function successManager(): BelongsTo
    {
        return $this->belongsTo(CompanyUserRelationship::class, self::FIELD_SUCCESS_MANAGER_ID, CompanyUserRelationship::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function businessDevelopmentManager(): BelongsTo
    {
        return $this->belongsTo(CompanyUserRelationship::class, self::FIELD_BUSINESS_DEVELOPMENT_MANAGER_ID, CompanyUserRelationship::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_ID, Invoice::FIELD_ID);
    }
}
