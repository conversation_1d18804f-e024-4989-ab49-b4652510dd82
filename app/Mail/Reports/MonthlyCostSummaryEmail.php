<?php

namespace App\Mail\Reports;

use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class MonthlyCostSummaryEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Collection $costSummaryData,
        public Company $company,
        public string $monthYear,
        public EloquentCompanyContact $contact
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Monthly Cost Summary - {$this->monthYear}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $totalCost = $this->costSummaryData->sum(function ($item) {
            return (float) str_replace(',', '', $item['total_cost']);
        });

        $totalAssignments = $this->costSummaryData->sum('assignment_count');

        return new Content(
            markdown: 'emails.reports.monthly-cost-summary',
            with: [
                'costSummaryData' => $this->costSummaryData,
                'company' => $this->company,
                'monthYear' => $this->monthYear,
                'contact' => $this->contact,
                'totalCost' => number_format($totalCost, 2),
                'totalAssignments' => $totalAssignments,
            ],
        );
    }
}
