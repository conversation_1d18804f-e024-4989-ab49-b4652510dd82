<?php

namespace App\Repositories\Odin\ProductProcessing;

use App\Contracts\Repositories\Odin\ProductProcessingRepositoryContract;
use App\Enums\Odin\AllocationReason;
use App\Jobs\CalculatePotentialRevenue;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingHeartbeat;
use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\LeadProcessingUnderReview;
use App\Models\LeadProcessor;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\PingPostAffiliates\LeadProcessingAffiliate;
use App\Models\RecycledLeads\LeadProcessingAged;
use App\Models\User;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use http\Exception\InvalidArgumentException;
use Illuminate\Support\Collection;

class ProductProcessingRepository implements ProductProcessingRepositoryContract
{

    /**
     * @param ConsumerProductRepository $consumerProductRepository
     */
    public function __construct(protected ConsumerProductRepository $consumerProductRepository){}

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @return bool
     */
    public function markLeadAsPendingReview(ConsumerProduct $consumerProduct, LeadProcessor $processor, string $reason): bool
    {
        LeadProcessingPendingReview::query()->updateOrCreate([
            LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            LeadProcessingPendingReview::FIELD_USER_ID             => $processor->id,
        ], [
            LeadProcessingPendingReview::FIELD_REASON  => $reason,
            LeadProcessingPendingReview::FIELD_LEAD_ID => ProductProcessingService::getLegacyId($consumerProduct),
        ]);

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $existingReason
     * @return bool
     */
    public function markLeadAsUnderReview(ConsumerProduct $consumerProduct, LeadProcessor $processor, string $reason, ?string $existingReason = null): bool
    {
        $model = LeadProcessingUnderReview::query()->updateOrCreate(
            [
                LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            ],
            [
                LeadProcessingUnderReview::FIELD_USER_ID         => $processor->id,
                LeadProcessingUnderReview::FIELD_REASON          => $reason,
                LeadProcessingUnderReview::FIELD_EXISTING_REASON => $existingReason,
                LeadProcessingUnderReview::FIELD_LOCATION_ID     => $consumerProduct->address->zip_code_location_id,
                LeadProcessingUnderReview::FIELD_LEAD_ID         => ProductProcessingService::getLegacyId($consumerProduct),
            ]
        );
        CalculatePotentialRevenue::dispatch($model->getKey());

        return true;
    }

    /**
     * @inheritDoc
     */
    public function removeInitial(ConsumerProduct $consumerProduct): bool
    {
        LeadProcessingInitial::query()
            ->where(LeadProcessingInitial::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::FIELD_ID})
            ->delete();

        return true;
    }

    /**
     * @inheritDoc
     */
    public function removePendingReview(ConsumerProduct $consumerProduct): bool
    {
        LeadProcessingPendingReview::query()
            ->where(LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::FIELD_ID})
            ->delete();

        return true;
    }

    /**
     * @inheritDoc
     */
    public function removeUnderReview(ConsumerProduct $consumerProduct): bool
    {
        LeadProcessingUnderReview::query()
            ->where(LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::FIELD_ID})
            ->delete();

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return bool
     */
    public function removeAged(ConsumerProduct $consumerProduct): bool
    {
        LeadProcessingAged::query()
            ->where(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->delete();

        if ($consumerProduct->clonedFrom) {
            LeadProcessingAged::query()
                ->where(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->cloned_from_id)
                ->delete();
        }

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return bool
     */
    public function removeAffiliate(ConsumerProduct $consumerProduct): bool
    {
        LeadProcessingAffiliate::query()
            ->where(LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->delete();

        if ($consumerProduct->clonedFrom) {
            LeadProcessingAffiliate::query()
                ->where(LeadProcessingAffiliate::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->cloned_from_id)
                ->delete();
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function updateProductStatus(ConsumerProduct $consumerProduct, int $status): bool
    {
        if(!in_array($status, ConsumerProduct::STATUSES)) throw new InvalidArgumentException("The requested status ($status) is invalid.");

        return $this->consumerProductRepository->updateConsumerProductModel(
            $consumerProduct,
            [ConsumerProduct::FIELD_STATUS => $status]
        );
    }

    /**
     * @inheritDoc
     */
    public function updateProductStatusReason(ConsumerProduct $consumerProduct, string $reason): bool
    {
        return $this->consumerProductRepository->updateConsumerProductData(
            $consumerProduct,
            [ConsumerProductRepository::PRODUCT_DATA_KEY_STATUS_REASON => $reason]
        );
    }

    /**
     * @inheritDoc
     */
    public function updateProductStatusAndReason(ConsumerProduct $consumerProduct, int $status, string $reason): bool
    {
        return $this->updateProductStatus($consumerProduct, $status)
            && $this->updateProductStatusReason($consumerProduct, $reason);
    }

    /**
     * @inheritDoc
     */
    public function addProductComment(ConsumerProduct $consumerProduct, string $comments): bool
    {
        return $this->consumerProductRepository->updateConsumerProductData(
            $consumerProduct,
            [ConsumerProductRepository::PRODUCT_DATA_KEY_COMMENTS => $comments]
        );
    }

    /**
     * @inheritDoc
     */
    public function updateBestTimeToCall(ConsumerProduct $consumerProduct, string $addBestTimeToContact): bool
    {
        return $this->consumerProductRepository->updateConsumerProductData(
            $consumerProduct,
            [ConsumerProductRepository::PRODUCT_DATA_KEY_BEST_TIME_TO_CONTACT => $addBestTimeToContact]
        );
    }

    /**
     * @inheritDoc
     */
    public function recordProcessorHistory(ConsumerProduct $consumerProduct, LeadProcessor $processor, string $action): int
    {
        if(!in_array($action, LeadProcessingHistory::ACTIONS)) return false;

        $history = new LeadProcessingHistory();

        $history->lead_processor_id      = $processor->id;
        $history->queue_configuration_id = $processor->team?->primary_queue_configuration_id;
        $history->action                 = $action;
        $history->consumer_product_id    = $consumerProduct->id;
        $history->lead_id                = ProductProcessingService::getLegacyId($consumerProduct);

        $history->save();

        return $history->id;
    }

    /**
     * @param Collection $consumerProductIds
     * @return Collection
     */
    public function getProcessorsWhoAllocatedProducts(Collection $consumerProductIds): Collection
    {
        return LeadProcessingHistory::query()
            ->whereIn(LeadProcessingHistory::FIELD_CONSUMER_PRODUCT_ID, $consumerProductIds)
            ->where(LeadProcessingHistory::FIELD_ACTION, LeadProcessingHistory::ACTION_ALLOCATED)
            ->distinct()
            ->get()
            ->keyBy(LeadProcessingHistory::FIELD_CONSUMER_PRODUCT_ID);
    }

    /**
     * @return LeadProcessor
     */
    public function getSystemProcessor(): LeadProcessor
    {
        $systemUser = User::where(User::FIELD_NAME, User::SYSTEM_USER_RESERVED_NAME)
            ->where(User::FIELD_EMAIL, User::SYSTEM_USER_RESERVED_EMAIL)
            ->withTrashed()
            ->first();
        if (!$systemUser){$systemUser = $this->createSystemUser();}

        $systemProcessor = LeadProcessor::where(LeadProcessor::FIELD_USER_ID, $systemUser->id)
            ->withTrashed()
            ->first();
        if (!$systemProcessor){$systemProcessor = $this->createSystemProcessor($systemUser);}

        return $systemProcessor;
    }

    /**
     * @param User $systemUser
     * @return LeadProcessor
     */
    private function createSystemProcessor(User $systemUser): LeadProcessor
    {
        $processor = LeadProcessor::create([
            LeadProcessor::FIELD_USER_ID => $systemUser->id,
            LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID => LeadProcessingTeam::NO_TEAM_ASSIGNED_ID
        ]);
        $processor->delete();

        return $processor;
    }

    /**
     * @return User
     */
    public function createSystemUser(): User
    {
        $user =  User::create([
            User::FIELD_NAME  => User::SYSTEM_USER_RESERVED_NAME,
            User::FIELD_EMAIL => User:: SYSTEM_USER_RESERVED_EMAIL
        ]);
        $user->delete();
        return $user;
    }

    /**
     * @inheritDoc
     */
    public function reserveProductToSystem(int $consumerProductId): bool
    {
        return $this->reserveProduct($consumerProductId, LeadProcessingReservedLead::SYSTEM_ID);
    }

    /**
     * @param int $consumerProductId
     * @param int $processorId
     * @return bool
     */
    public function reserveProduct(int $consumerProductId, int $processorId): bool
    {
        return !!LeadProcessingReservedLead::query()->updateOrCreate(
            [
                LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            ],
            [
                LeadProcessingReservedLead::FIELD_LEAD_ID             => ProductProcessingService::getLegacyId($consumerProductId),
                LeadProcessingReservedLead::FIELD_PROCESSOR_ID        => $processorId,
            ],
        );
    }

    /**
     * Unlocks a lead.
     *
     * @param int $consumerProductId
     * @return bool
     */
    public function releaseProduct(int $consumerProductId): bool
    {
        LeadProcessingReservedLead::query()->where(LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)->delete();

        return true;
    }

    /**
     * @inheritDoc
     */
    public function getTimezoneConfigurationByStandardUTCOffset(int $offset): ?LeadProcessingTimeZoneConfiguration
    {
        /** @var LeadProcessingTimeZoneConfiguration|null $timeZoneConfiguration */
        $timeZoneConfiguration = LeadProcessingTimeZoneConfiguration::query()
            ->where(LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, $offset)
            ->first();

        return $timeZoneConfiguration;
    }

    /**
     * @return bool
     */
    public function deleteStragglingReservedLeads(): bool
    {
        LeadProcessingReservedLead::query()
            ->whereHas(LeadProcessingReservedLead::RELATION_PRODUCT_ASSIGNMENT)
            ->delete();

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param string $allocationReason
     *
     * @return void
     */
    public function updateConsumerClassification(ConsumerProduct $consumerProduct, string $allocationReason): void
    {
        switch ($allocationReason) {
            case AllocationReason::VERIFIED_WITHOUT_SPEAKING_TO->value:
            case AllocationReason::NO_VALIDATION_REQUITED->value:
                $consumerProduct->consumer->classification = Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING;
                break;
            case AllocationReason::QUALIFIED_VIA_CONVERSATION->value:
                $consumerProduct->consumer->classification = Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL;
                break;
            default:
                logger()->error('Invalid allocation reason');
                return;
        }

        $consumerProduct->consumer->save();
    }

    /**
     * Returns a lead processor by their user id.
     *
     * @param int $userId
     * @return LeadProcessor|null
     */
    public function getLeadProcessorByUserId(int $userId): ?LeadProcessor
    {
        /** @var LeadProcessor|null $processor */
        $processor = LeadProcessor::query()->where(LeadProcessor::FIELD_USER_ID, $userId)->first();

        return $processor;
    }

    /**
     * Returns the global configuration for lead processing.
     *
     * @return LeadProcessingConfiguration|null
     */
    public function getLeadProcessingConfiguration(): ?LeadProcessingConfiguration
    {
        /** @var LeadProcessingConfiguration|null $model */
        $model = LeadProcessingConfiguration::query()->first();

        return $model;
    }

    /**
     * @param ConsumerProduct $product
     * @return bool
     */
    public function productHadOffHourSales(ConsumerProduct $product): bool
    {
        return $product->productAssignment()
            ->where(ProductAssignment::FIELD_OFF_HOUR_SALE, true)
            ->exists();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @return bool
     */
    public function hasHeartbeat(ConsumerProduct $consumerProduct, LeadProcessor $processor): bool
    {
        return LeadProcessingHeartbeat::query()
                ->where(LeadProcessingHeartbeat::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
                ->where(LeadProcessingHeartbeat::FIELD_LEAD_PROCESSOR_ID, $processor->id)
                ->exists();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @return LeadProcessingHeartbeat|null
     */
    public function getHeartbeat(ConsumerProduct $consumerProduct, LeadProcessor $processor): ?LeadProcessingHeartbeat
    {
        return LeadProcessingHeartbeat::query()
            ->where(LeadProcessingHeartbeat::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->where(LeadProcessingHeartbeat::FIELD_LEAD_PROCESSOR_ID, $processor->id)
            ->first();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @return void
     */
    public function processHeartbeat(ConsumerProduct $consumerProduct, LeadProcessor $processor): void
    {
        LeadProcessingHeartbeat::query()
            ->updateOrCreate(
                [
                    LeadProcessingHeartbeat::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                    LeadProcessingHeartbeat::FIELD_LEAD_PROCESSOR_ID   => $processor->id
                ], [
                    LeadProcessingHeartbeat::FIELD_LAST_HEARTBEAT => now()->timestamp,
                    LeadProcessingHeartbeat::FIELD_LEAD_ID        => ProductProcessingService::getLegacyId($consumerProduct),
                ]
            );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @return void
     */
    public function removeHeartbeat(ConsumerProduct $consumerProduct, LeadProcessor $processor): void
    {
        LeadProcessingHeartbeat::query()
            ->where(LeadProcessingHeartbeat::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->where(LeadProcessingHeartbeat::FIELD_LEAD_PROCESSOR_ID, $processor->id)
            ->delete();
    }
}
