<?php

namespace App\Listeners;

use App\Enums\Company\CompanySystemStatus;
use App\Events\Company\CompanySuspendedEvent;
use App\Jobs\Emails\SendGenericEmailJob;
use App\Models\Odin\Company;
use App\Models\User;
use App\Notifications\Slack\CompanyAlertsSlackNotification;
use App\Services\Slack\SlackNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CompanySuspendedEventListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(protected SlackNotificationService $slackNotificationService) {}

    /**
     * Handle the event.
     */
    public function handle(CompanySuspendedEvent $event): void
    {
        $companyCreatedToday = $event->company->created_at->isToday();
        $companySuspended = in_array($event->company->system_status, [
            CompanySystemStatus::SUSPENDED_PAYMENT,
            CompanySystemStatus::SUSPENDED_PAYMENT_METHOD,
            CompanySystemStatus::SUSPENDED_CRM_REJECTION_THRESHOLD
        ]);

        if ($companySuspended && !$companyCreatedToday) {
                $this->slackNotificationService->sendSlackNotification(
                    new CompanyAlertsSlackNotification($event->company, " has been suspended"));
                $this->notifyCompanyManagers($event->company);
        }
    }

    /**
     * @param Company $company
     *
     * @return void
     */
    protected function notifyCompanyManagers(Company $company): void
    {
        collect([$company->businessDevelopmentManager, $company->accountManager, $company->onboardingManager])
            ->filter()
            ->each(fn(User $user) => $this->notify($user, $company));
    }

    /**
     * @param User $user
     * @param Company $company
     *
     * @return void
     */
    protected function notify(User $user, Company $company): void
    {
        SendGenericEmailJob::dispatch(
            $user->email,
            "$company->name has been suspended",
            null,
            $this->getContent($user, $company)
        );
    }

    /**
     * @param User $user
     * @param Company $company
     *
     * @return string
     */
    protected function getContent(User $user, Company $company): string
    {
        $companyUrl = config('app.url') . "/companies/$company->id";
        $sender = config('app.name');
        $reason = $company->system_status->label();

        return <<<CONTENT
Hi {$user->name},

<a href="$companyUrl">$company->name</a> has been suspended.<br><br>
Reason: <b>$reason</b>

Regards,<br>
$sender
CONTENT;
    }
}
