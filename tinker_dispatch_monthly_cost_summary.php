
/**
 * Tinker script to manually dispatch the SendMonthlyCostSummaryEmailJob
 *
 * Usage:
 * php artisan tinker
 * include 'tinker_dispatch_monthly_cost_summary.php';
 *
 * Or run directly:
 * php artisan tinker < tinker_dispatch_monthly_cost_summary.php
 */

use App\Jobs\Reports\SendMonthlyCostSummaryEmailJob;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;

echo "=== Monthly Cost Summary Email Job Dispatch Script ===\n\n";

// Get the company ID from the job constant
$companyId = SendMonthlyCostSummaryEmailJob::COMPANY_ID;
echo "Company ID from job: {$companyId}\n";

// Check if company exists
$company = Company::find($companyId);
if (!$company) {
    echo "❌ ERROR: Company with ID {$companyId} not found!\n";
    echo "Available companies (first 10):\n";
    Company::take(10)->get(['id', 'name'])->each(function($c) {
        echo "  - ID: {$c->id}, Name: {$c->name}\n";
    });
    exit;
}

echo "✅ Company found: {$company->name}\n";

// Check for active company users/contacts
$activeContacts = CompanyUser::query()
    ->where(CompanyUser::FIELD_COMPANY_ID, $company->id)
    ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
    ->where(CompanyUser::FIELD_IS_CONTACT, CompanyUser::USER_IS_CONTACT)
    ->whereNotNull(CompanyUser::FIELD_EMAIL)
    ->where(CompanyUser::FIELD_EMAIL, '!=', '')
    ->get();

echo "📧 Active contacts found: " . $activeContacts->count() . "\n";
if ($activeContacts->count() > 0) {
    echo "Contact emails:\n";
    $activeContacts->each(function($contact) {
        echo "  - {$contact->first_name} {$contact->last_name} ({$contact->email})\n";
    });
} else {
    echo "⚠️  WARNING: No active contacts found for this company!\n";
}

// Check for product assignments from start of month to today
$startOfMonth = now()->startOfYear();
$today = now();

$assignmentCount = ProductAssignment::query()
    ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
    ->where(ProductAssignment::FIELD_DELIVERED, true)
    ->where(ProductAssignment::FIELD_CHARGEABLE, true)
    ->whereBetween(ProductAssignment::CREATED_AT, [$startOfMonth, $today])
    ->count();

echo "📊 Product assignments this month to date: {$assignmentCount}\n";
echo "📅 Date range: {$startOfMonth->format('Y-m-d')} to {$today->format('Y-m-d')}\n\n";

if ($assignmentCount === 0) {
    echo "⚠️  WARNING: No product assignments found for this month to date. Email will be sent but will be empty.\n\n";
}

// Ask for confirmation
echo "Do you want to dispatch the job? (y/N): ";
$handle = fopen("php://stdin", "r");
$confirmation = trim(fgets($handle));
fclose($handle);

if (strtolower($confirmation) !== 'y' && strtolower($confirmation) !== 'yes') {
    echo "❌ Job dispatch cancelled.\n";
    // exit;
}

echo "\n🚀 Dispatching SendMonthlyCostSummaryEmailJob...\n";

try {
    // Dispatch the job
    $job = new SendMonthlyCostSummaryEmailJob();

    // You can dispatch to different queues or connections if needed:
    // $job->dispatch()->onQueue('email_notification');
    // $job->dispatch()->onConnection('redis');

    // Dispatch with default settings (will use email_notification queue as configured)
    dispatch($job);

    echo "✅ Job dispatched successfully!\n";
    echo "📧 The job will process and send emails to {$activeContacts->count()} contacts.\n";
    echo "🔍 Check your queue worker logs and email delivery for results.\n\n";

    echo "To monitor the job:\n";
    echo "- Check queue: php artisan queue:work\n";
    echo "- Check failed jobs: php artisan queue:failed\n";
    echo "- Check logs: tail -f storage/logs/laravel.log\n";

} catch (Exception $e) {
    echo "❌ ERROR dispatching job: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Script completed ===\n";
