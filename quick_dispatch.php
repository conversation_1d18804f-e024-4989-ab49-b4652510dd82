<?php

/**
 * Quick dispatch script for SendMonthlyCostSummaryEmailJob
 * 
 * Usage in tinker:
 * include 'quick_dispatch.php';
 */

use App\Jobs\Reports\SendMonthlyCostSummaryEmailJob;

echo "Dispatching SendMonthlyCostSummaryEmailJob...\n";

// Dispatch the job
dispatch(new SendMonthlyCostSummaryEmailJob());

echo "✅ Job dispatched successfully!\n";
echo "Company ID: " . SendMonthlyCostSummaryEmailJob::COMPANY_ID . "\n";
echo "Check your queue worker for processing.\n";
